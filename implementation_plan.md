# External Platform System - Implementation Plan

## Overview

This document outlines the implementation plan to achieve **80-90% feature parity** between external platforms and built-in iOS/Android platforms while **minimizing changes to existing Expo code**. The plan focuses on high-impact improvements that work within the "bolted-on" architectural constraint.

## Phase 1: High-Impact, Low-Change Improvements (Immediate Priority)

### 1.1 Implement Run Command Delegation

**Current State**: External platforms delegate to React Native CLI, missing Expo integration.

**Solution**: Add `runAsync` delegation to achieve 90-95% parity with minimal changes.

**Implementation**: Add delegation logic to run command routing (5-10 lines) and `runAsync` interface.

#### Files to Modify:
1. `packages/@expo/cli/src/run/index.ts` - Add delegation logic (5-10 lines)
2. `packages/@expo/cli/src/core/PlatformRegistry.ts` - Add `runAsync` to interface (1 property)

#### Implementation Steps:

**Step 1: Add runAsync to External Platform Interface**
```typescript
// packages/@expo/cli/src/core/PlatformRegistry.ts
export interface ExternalPlatform {
  platform: string;
  displayName?: string;

  // Existing properties...
  resolveDeviceAsync?: ExternalPlatformDeviceResolver<any>;
  platformManagerConstructor?: ExternalPlatformManagerConstructor<any>;
  configPlugins?: string[];
  metroExtensions?: string[];
  templatePath?: string;
  autolinkingImplementation?: AutolinkingImplementation;

  // NEW: Optional run function for native integration
  runAsync?: (projectRoot: string, options: RunOptions) => Promise<void>;
}
```

**Step 2: Update Run Command Routing**
```typescript
// packages/@expo/cli/src/run/index.ts
export const expoRun: Command = async (argv) => {
  // ... existing code ...
  
  if (platform === 'ios') {
    const { expoRunIos } = await import('./ios/index.js');
    return expoRunIos(argsWithoutPlatform);
  } else if (platform === 'android') {
    const { expoRunAndroid } = await import('./android/index.js');
    return expoRunAndroid(argsWithoutPlatform);
  } else if (externalPlatforms.includes(platform)) {
    // NEW: Native external platform integration
    const { runExternalPlatformNativeAsync } = await import('./external/runExternalPlatformNativeAsync.js');
    return runExternalPlatformNativeAsync(platform, argsWithoutPlatform);
  } else {
    // Error handling
  }
};
```

**Step 3: Implement Native External Platform Runner**
```typescript
// packages/@expo/cli/src/run/external/runExternalPlatformNativeAsync.ts
export async function runExternalPlatformNativeAsync(
  platform: string,
  args: string[]
): Promise<void> {
  const platformData = platformRegistry.getPlatform(platform);
  if (!platformData) {
    throw new CommandError(`Platform "${platform}" not found`);
  }
  
  const runner = new ExternalPlatformRunner(platformData);
  const options = parseRunOptions(args);
  
  return runner.runAsync({
    ...options,
    platform,
    projectRoot: process.cwd(),
  });
}
```

### 1.2 Standardize Device Management

**Current Problem**: External platforms use different device management interfaces and patterns.

**Solution**: Create standardized device management interfaces that external platforms must implement.

#### Files to Modify:
1. `packages/@expo/cli/src/core/PlatformRegistry.ts` - Update interfaces
2. `packages/@expo/cli/src/start/platforms/DeviceManager.ts` - Create base classes
3. External platform packages - Update implementations

#### Implementation Steps:

**Step 1: Create Standardized Device Manager Base Class**
```typescript
// packages/@expo/cli/src/start/platforms/ExternalDeviceManager.ts
export abstract class ExternalDeviceManager<TDevice> extends DeviceManager<TDevice> {
  abstract static resolveAsync(
    options?: BaseResolveDeviceProps<Partial<TDevice>>
  ): Promise<ExternalDeviceManager<TDevice>>;
  
  abstract getDeviceType(): 'simulator' | 'emulator' | 'physical';
  abstract validateDevice(): Promise<boolean>;
  abstract launchSimulator?(): Promise<void>;
  abstract installApp(appPath: string): Promise<void>;
  abstract launchApp(appId: string): Promise<void>;
}
```

**Step 2: Update External Platform Interface**
```typescript
// packages/@expo/cli/src/core/PlatformRegistry.ts
export interface ExternalPlatform {
  // ... existing properties ...
  
  // UPDATED: Standardized device management
  deviceManagerClass: typeof ExternalDeviceManager;
  
  // DEPRECATED: Remove these in favor of deviceManagerClass
  // resolveDeviceAsync?: ExternalPlatformDeviceResolver<any>;
  // platformManagerConstructor?: ExternalPlatformManagerConstructor<any>;
}
```

**Step 3: Create Device Manager Factory**
```typescript
// packages/@expo/cli/src/start/platforms/DeviceManagerFactory.ts
export class DeviceManagerFactory {
  static async createForPlatform(
    platform: string,
    options?: BaseResolveDeviceProps<any>
  ): Promise<DeviceManager<any>> {
    if (platform === 'ios') {
      return AppleDeviceManager.resolveAsync(options);
    } else if (platform === 'android') {
      return AndroidDeviceManager.resolveAsync(options);
    } else {
      const platformData = platformRegistry.getPlatform(platform);
      if (!platformData?.deviceManagerClass) {
        throw new CommandError(`Platform "${platform}" does not provide device management`);
      }
      return platformData.deviceManagerClass.resolveAsync(options);
    }
  }
}
```

### 1.3 Add Install Command Integration

**Current Problem**: Install command has no external platform awareness.

**Solution**: Add platform-specific dependency resolution and autolinking integration.

#### Files to Modify:
1. `packages/@expo/cli/src/install/installAsync.ts` - Add platform logic
2. `packages/@expo/cli/src/install/resolveOptions.ts` - Add platform options
3. Create external platform dependency resolver

#### Implementation Steps:

**Step 1: Create Platform Dependency Resolver**
```typescript
// packages/@expo/cli/src/install/ExternalPlatformDependencyResolver.ts
export interface PlatformDependency {
  name: string;
  version?: string;
  platform: string;
  required: boolean;
  autolinkingConfig?: any;
}

export class ExternalPlatformDependencyResolver {
  static async resolveDependencies(
    packages: string[],
    platforms: string[]
  ): Promise<PlatformDependency[]> {
    const dependencies: PlatformDependency[] = [];
    
    for (const platform of platforms) {
      const platformData = platformRegistry.getPlatform(platform);
      if (platformData?.dependencyResolver) {
        const platformDeps = await platformData.dependencyResolver.resolve(packages);
        dependencies.push(...platformDeps);
      }
    }
    
    return dependencies;
  }
}
```

**Step 2: Update Install Command**
```typescript
// packages/@expo/cli/src/install/installAsync.ts
export async function installAsync(
  packages: string[],
  options: Options,
  extras: string[]
): Promise<void> {
  // ... existing logic ...
  
  // NEW: Resolve platform-specific dependencies
  const { exp } = getConfig(projectRoot);
  const platforms = exp.platforms || ['ios', 'android'];
  const externalPlatforms = platforms.filter(p => !['ios', 'android', 'web'].includes(p));
  
  if (externalPlatforms.length > 0) {
    const platformDeps = await ExternalPlatformDependencyResolver.resolveDependencies(
      packages,
      externalPlatforms
    );
    
    // Add platform-specific packages to installation
    for (const dep of platformDeps) {
      if (dep.required) {
        packages.push(dep.version ? `${dep.name}@${dep.version}` : dep.name);
      }
    }
  }
  
  // ... continue with installation ...
  
  // NEW: Trigger platform-specific autolinking
  await triggerExternalPlatformAutolinking(projectRoot, externalPlatforms);
}
```

### 1.4 Add Doctor Command Integration

**Current Problem**: Doctor command is unaware of external platforms.

**Solution**: Add external platform health checking and prerequisite validation.

#### Files to Modify:
1. `packages/@expo/cli/src/doctor/index.ts` - Add platform checks
2. Create external platform prerequisite system
3. Add platform-specific validation

#### Implementation Steps:

**Step 1: Create External Platform Prerequisite Interface**
```typescript
// packages/@expo/cli/src/start/doctor/ExternalPlatformPrerequisite.ts
export abstract class ExternalPlatformPrerequisite extends Prerequisite {
  constructor(protected platform: string) {
    super();
  }
  
  abstract checkDevelopmentEnvironment(): Promise<boolean>;
  abstract checkSystemRequirements(): Promise<boolean>;
  abstract checkPlatformTools(): Promise<boolean>;
  abstract getInstallationInstructions(): string[];
}
```

**Step 2: Update Doctor Command**
```typescript
// packages/@expo/cli/src/doctor/index.ts
export async function doctorAsync(projectRoot: string): Promise<void> {
  // ... existing checks ...
  
  // NEW: Check external platforms
  const { exp } = getConfig(projectRoot);
  const platforms = exp.platforms || ['ios', 'android'];
  const externalPlatforms = platforms.filter(p => !['ios', 'android', 'web'].includes(p));
  
  for (const platform of externalPlatforms) {
    const platformData = platformRegistry.getPlatform(platform);
    if (platformData?.prerequisiteClass) {
      const prerequisite = new platformData.prerequisiteClass(platform);
      await prerequisite.assertAsync();
    }
  }
}
```

## Phase 2: High Priority Fixes

### 2.1 Standardize Error Handling

**Implementation**: Create consistent error handling patterns across all external platform integration points.

### 2.2 Enhance Autolinking

**Implementation**: Improve native module integration and configuration generation for external platforms.

### 2.3 Standardize Platform Managers

**Implementation**: Create consistent platform manager patterns and constructor interfaces.

## Phase 3: Polish and Optimization

### 3.1 Improve Template Validation
### 3.2 Optimize URL Generation  
### 3.3 Enhance Plugin Validation

## Implementation Timeline

### Week 1-2: Native Run Integration
- Implement ExternalPlatformRunner
- Update run command routing
- Create native run implementation
- Test with Windows platform

### Week 3-4: Device Management Standardization
- Create ExternalDeviceManager base class
- Update platform interfaces
- Implement DeviceManagerFactory
- Update external platform packages

### Week 5-6: Install and Doctor Integration
- Implement platform dependency resolver
- Update install command
- Create platform prerequisite system
- Update doctor command

### Week 7-8: Testing and Validation
- Comprehensive integration testing
- Performance testing
- End-to-end workflow testing
- Bug fixes and optimization

## Success Metrics

1. **Command Parity**: `expo run <platform>` works identically for external platforms
2. **Device Management**: Consistent device selection and launching experience
3. **Install Integration**: Platform-specific dependencies resolved automatically
4. **Doctor Integration**: External platform environments validated
5. **Performance**: No regression in any workflow
6. **Developer Experience**: Seamless integration indistinguishable from built-in platforms

## Risk Mitigation

1. **Breaking Changes**: Implement backward compatibility for existing external platforms
2. **Performance Impact**: Optimize platform loading and registration
3. **Testing Coverage**: Comprehensive test suite for all integration points
4. **Documentation**: Update all documentation and migration guides

## Next Steps

1. Review and approve implementation plan
2. Create detailed technical specifications for each component
3. Begin implementation with Phase 1 critical fixes
4. Set up comprehensive testing infrastructure
5. Coordinate with external platform maintainers for updates
