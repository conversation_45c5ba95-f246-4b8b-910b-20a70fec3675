# External Platform Device Management Guide

## Overview

External platforms should provide device management by extending the `DeviceManager<TDevice>` abstract class. This ensures consistent device management across all platforms in Expo CLI.

## Implementation Pattern

### 1. Define Your Device Type

```typescript
interface MyPlatformDevice {
  id: string;
  name: string;
  type: 'simulator' | 'emulator' | 'physical';
  isAvailable: boolean;
  // Add platform-specific properties
  arch?: string;
  version?: string;
}
```

### 2. Extend DeviceManager

```typescript
import { DeviceManager } from '@expo/cli/src/start/platforms/DeviceManager';
import { BaseResolveDeviceProps } from '@expo/cli/src/start/platforms/PlatformManager';

export class MyPlatformDeviceManager extends DeviceManager<MyPlatformDevice> {
  /**
   * Static method to resolve and create a device manager instance.
   * This is the primary entry point for device resolution.
   */
  static async resolveAsync(
    options?: BaseResolveDeviceProps<MyPlatformDevice>
  ): Promise<MyPlatformDeviceManager> {
    // 1. Get available devices
    const devices = await getAvailableDevices();
    
    // 2. Select device based on options
    let selectedDevice: MyPlatformDevice;
    
    if (options?.device) {
      // Find specific device
      selectedDevice = findDevice(devices, options.device);
    } else if (options?.shouldPrompt) {
      // Prompt user to select device
      selectedDevice = await promptForDevice(devices);
    } else {
      // Use first available device
      selectedDevice = devices.find(d => d.isAvailable) || devices[0];
    }
    
    if (!selectedDevice) {
      throw new Error('No devices available');
    }
    
    return new MyPlatformDeviceManager(selectedDevice);
  }

  // Required property implementations
  get name(): string {
    return this.device.name;
  }

  get identifier(): string {
    return this.device.id;
  }

  // Required method implementations
  async startAsync(): Promise<MyPlatformDevice> {
    // Start/boot the device if needed
    if (this.device.type !== 'physical') {
      await startSimulator(this.device);
    }
    return this.device;
  }

  async getAppVersionAsync(
    applicationId: string,
    options?: { containerPath?: string }
  ): Promise<string | null> {
    // Get app version from device
    return getAppVersion(this.device, applicationId);
  }

  async installAppAsync(binaryPath: string): Promise<void> {
    // Install app on device
    await installApp(this.device, binaryPath);
  }

  async uninstallAppAsync(applicationId: string): Promise<void> {
    // Uninstall app from device
    await uninstallApp(this.device, applicationId);
  }

  async isAppInstalledAndIfSoReturnContainerPathForIOSAsync(
    applicationId: string
  ): Promise<boolean | string> {
    // Check if app is installed (return container path for iOS-like platforms)
    const isInstalled = await isAppInstalled(this.device, applicationId);
    return isInstalled;
  }

  async openUrlAsync(url: string, options?: { appId?: string }): Promise<void> {
    // Open URL on device
    await openUrl(this.device, url, options?.appId);
  }

  async activateWindowAsync(): Promise<void> {
    // Bring device window to foreground
    await activateWindow(this.device);
  }

  async ensureExpoGoAsync(sdkVersion: string): Promise<boolean> {
    // Ensure Expo Go is installed and compatible
    return ensureExpoGo(this.device, sdkVersion);
  }

  getExpoGoAppId(): string {
    // Return platform-specific Expo Go app identifier
    return 'com.expo.client'; // Example
  }
}
```

### 3. Register with External Platform

```typescript
import { ExternalPlatform } from '@expo/cli/src/core/PlatformRegistry';

export const myPlatform: ExternalPlatform = {
  platform: 'myplatform',
  displayName: 'My Platform',
  
  // Device management integration
  deviceManagerConstructor: MyPlatformDeviceManager,
  
  // Other integrations...
};
```

## Key Requirements

### Device Manager Class Must:

1. **Extend `DeviceManager<TDevice>`** - Use the abstract base class
2. **Implement static `resolveAsync` method** - Primary entry point for device resolution
3. **Implement all abstract methods** - Required by the base class
4. **Handle device discovery** - Find available devices for the platform
5. **Support device selection** - Handle specific device targeting and prompting

### Device Type Should:

1. **Have unique identifier** - `id` property for device identification
2. **Have human-readable name** - `name` property for display
3. **Indicate availability** - `isAvailable` boolean for device status
4. **Include type information** - Help CLI understand device capabilities

## Benefits of This Approach

### ✅ **Consistency**
- All platforms use the same `DeviceManager` interface
- Consistent device resolution patterns across platforms
- Standardized method signatures and behavior

### ✅ **Type Safety**
- Full TypeScript support with generic device types
- Compile-time validation of device manager implementations
- IntelliSense support for external platform developers

### ✅ **Integration**
- Seamless integration with Expo CLI's device management
- Works with existing dev server and debugging tools
- Compatible with all CLI commands that use device management

### ✅ **Flexibility**
- Platform-specific device types and properties
- Custom device discovery and selection logic
- Platform-specific app installation and management

## Example: Windows Platform

```typescript
interface WindowsDevice {
  id: string;
  name: string;
  type: 'desktop';
  isAvailable: boolean;
  arch: 'x64' | 'ARM64';
}

export class WindowsDeviceManager extends DeviceManager<WindowsDevice> {
  static async resolveAsync(
    options?: BaseResolveDeviceProps<WindowsDevice>
  ): Promise<WindowsDeviceManager> {
    // Windows typically has one "device" - the local machine
    const device: WindowsDevice = {
      id: 'local',
      name: 'Local Machine',
      type: 'desktop',
      isAvailable: true,
      arch: process.arch as 'x64' | 'ARM64',
    };
    
    return new WindowsDeviceManager(device);
  }
  
  // ... implement all required methods
}
```

This approach provides 100% feature parity with built-in platforms while maintaining the simplicity and consistency that external platform developers need.
