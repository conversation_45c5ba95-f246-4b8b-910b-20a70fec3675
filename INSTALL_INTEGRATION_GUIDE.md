# External Platform Install Command Integration Guide

## Overview

External platforms can now integrate with the `expo install` command to provide platform-specific dependency resolution and autolinking configuration. This ensures that when users install packages, any platform-specific dependencies are automatically resolved and configured.

## Implementation

### 1. Create Dependency Resolver

External platforms should implement the `ExternalPlatformDependencyResolver` interface:

```typescript
import { ExternalPlatformDependencyResolver } from '@expo/cli/src/core/PlatformRegistry';

export class WindowsDependencyResolver implements ExternalPlatformDependencyResolver {
  /**
   * Resolve platform-specific dependencies for given packages.
   */
  async resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]> {
    const additionalPackages: string[] = [];
    
    // Example: If react-native-vector-icons is being installed, 
    // also install the Windows-specific package
    if (packages.some(pkg => pkg.startsWith('react-native-vector-icons'))) {
      additionalPackages.push('react-native-vector-icons-windows');
    }
    
    // Example: If @react-native-async-storage/async-storage is being installed,
    // ensure Windows-specific configuration
    if (packages.some(pkg => pkg.includes('async-storage'))) {
      additionalPackages.push('react-native-windows-async-storage');
    }
    
    // Example: SDK version-specific dependencies
    if (sdkVersion === '50.0.0') {
      // Add SDK 50-specific Windows packages
      additionalPackages.push('react-native-windows@0.73.x');
    }
    
    return additionalPackages;
  }
  
  /**
   * Configure autolinking after packages are installed.
   */
  async configureAutolinking(projectRoot: string, packages: string[]): Promise<void> {
    const fs = require('fs');
    const path = require('path');
    
    // Example: Update Windows-specific autolinking configuration
    const windowsDir = path.join(projectRoot, 'windows');
    if (fs.existsSync(windowsDir)) {
      // Update packages.config or other Windows-specific files
      await this.updateWindowsPackageConfig(windowsDir, packages);
      
      // Regenerate autolinking files
      await this.regenerateAutolinkingFiles(projectRoot);
    }
  }
  
  private async updateWindowsPackageConfig(windowsDir: string, packages: string[]): Promise<void> {
    // Platform-specific implementation
    // Update packages.config, project files, etc.
  }
  
  private async regenerateAutolinkingFiles(projectRoot: string): Promise<void> {
    // Platform-specific implementation
    // Regenerate autolinking configuration files
  }
}
```

### 2. Register with External Platform

Add the dependency resolver to your platform registration:

```typescript
import { ExternalPlatform } from '@expo/cli/src/core/PlatformRegistry';
import { WindowsDependencyResolver } from './WindowsDependencyResolver';

export const windowsPlatform: ExternalPlatform = {
  platform: 'windows',
  displayName: 'Windows',
  
  // Install command integration
  dependencyResolver: new WindowsDependencyResolver(),
  
  // Other platform integrations...
  deviceManagerConstructor: WindowsDeviceManager,
  runAsync: runWindowsAsync,
};
```

## Usage Examples

### Basic Package Installation

When users run standard install commands, external platforms automatically resolve additional dependencies:

```bash
# User runs this command
expo install react-native-vector-icons

# External platforms automatically resolve additional packages:
# - react-native-vector-icons (requested)
# - react-native-vector-icons-windows (resolved by Windows platform)
# - react-native-vector-icons-macos (resolved by macOS platform)
```

### Platform-Specific Dependencies

External platforms can resolve dependencies based on the packages being installed:

```typescript
async resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]> {
  const additionalPackages: string[] = [];
  
  for (const pkg of packages) {
    // Check if this package needs platform-specific dependencies
    if (pkg.startsWith('react-native-camera')) {
      additionalPackages.push('react-native-windows-camera');
    }
    
    if (pkg.includes('sqlite')) {
      additionalPackages.push('react-native-windows-sqlite');
    }
    
    if (pkg.includes('maps')) {
      additionalPackages.push('react-native-windows-maps');
    }
  }
  
  return additionalPackages;
}
```

### SDK Version-Specific Resolution

Platforms can provide different dependencies based on the Expo SDK version:

```typescript
async resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]> {
  const additionalPackages: string[] = [];
  
  // SDK version-specific logic
  const majorVersion = parseInt(sdkVersion.split('.')[0]);
  
  if (majorVersion >= 50) {
    // Use new architecture packages for SDK 50+
    if (packages.some(pkg => pkg.includes('react-native-reanimated'))) {
      additionalPackages.push('react-native-reanimated-windows@3.x');
    }
  } else {
    // Use legacy packages for older SDK versions
    if (packages.some(pkg => pkg.includes('react-native-reanimated'))) {
      additionalPackages.push('react-native-reanimated-windows@2.x');
    }
  }
  
  return additionalPackages;
}
```

### Autolinking Configuration

After packages are installed, platforms can configure autolinking:

```typescript
async configureAutolinking(projectRoot: string, packages: string[]): Promise<void> {
  // Update platform-specific autolinking configuration
  await this.updateReactNativeConfig(projectRoot, packages);
  await this.updatePlatformProjectFiles(projectRoot, packages);
  await this.generateAutolinkingFiles(projectRoot);
}

private async updateReactNativeConfig(projectRoot: string, packages: string[]): Promise<void> {
  const configPath = path.join(projectRoot, 'react-native.config.js');
  
  // Read existing config
  let config = {};
  if (fs.existsSync(configPath)) {
    config = require(configPath);
  }
  
  // Add platform-specific configuration
  config.platforms = config.platforms || {};
  config.platforms.windows = {
    sourceDir: '../windows',
    solutionFile: 'MyApp.sln',
    project: {
      projectFile: 'MyApp/MyApp.vcxproj',
    },
  };
  
  // Write updated config
  fs.writeFileSync(configPath, `module.exports = ${JSON.stringify(config, null, 2)};`);
}
```

## Benefits

### ✅ **Automatic Dependency Resolution**
- External platforms automatically resolve platform-specific dependencies
- Users don't need to manually install platform-specific packages
- Consistent dependency versions across platforms

### ✅ **Seamless Integration**
- Works with existing `expo install` command
- No new commands or workflows for users to learn
- Backward compatible with existing projects

### ✅ **Platform-Specific Configuration**
- Platforms can configure autolinking after installation
- Platform-specific project files are automatically updated
- Reduces manual configuration steps for developers

### ✅ **SDK Version Awareness**
- Dependency resolution can vary based on Expo SDK version
- Ensures compatibility with different SDK versions
- Supports migration between SDK versions

## Error Handling

The install integration is designed to be resilient:

- **Dependency resolution errors**: Logged as warnings, installation continues
- **Autolinking configuration errors**: Logged as warnings, doesn't fail installation
- **Platform not found**: Silently skipped, no impact on installation
- **Missing dependency resolver**: Silently skipped, no impact on installation

This ensures that external platform issues don't break the core install functionality.

## Testing

External platforms should test their dependency resolution:

```typescript
describe('WindowsDependencyResolver', () => {
  it('should resolve Windows-specific dependencies', async () => {
    const resolver = new WindowsDependencyResolver();
    const result = await resolver.resolveDependencies(['react-native-vector-icons'], '50.0.0');
    
    expect(result).toContain('react-native-vector-icons-windows');
  });
  
  it('should configure autolinking after installation', async () => {
    const resolver = new WindowsDependencyResolver();
    await resolver.configureAutolinking('/test/project', ['react-native-vector-icons']);
    
    // Verify autolinking configuration was updated
  });
});
```

This integration provides external platforms with powerful dependency management capabilities while maintaining the simplicity and reliability of the core install command.
