{"name": "expo-doctor", "version": "1.12.5", "main": "build/index.js", "description": "Check your Expo project for known issues", "keywords": ["expo", "ios", "expo-doctor", "android"], "homepage": "https://docs.expo.dev", "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-doctor"}, "author": "Expo <<EMAIL>>", "license": "MIT", "bin": "./build/index.js", "files": ["build"], "scripts": {"build": "ncc build ./src/index.ts -o build/", "build:prod": "ncc build ./src/index.ts -o build/ --minify --no-cache --no-source-map-register", "prepare": "yarn run clean && yarn run build:prod", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "watch": "yarn run build -w", "prepublishOnly": "expo-module prepublishOnly"}, "devDependencies": {"@expo/cli": "*", "@expo/config": "~10.0.11", "@expo/json-file": "~9.0.2", "@expo/schemer": "1.5.4", "@expo/spawn-async": "^1.7.2", "@types/debug": "^4.1.8", "@vercel/ncc": "0.38.1", "chalk": "^4.0.0", "debug": "^4.3.4", "expo-module-scripts": "^4.0.5", "fast-glob": "^3.3.2", "getenv": "^1.0.0", "ignore": "^5.3.2", "ora": "3.4.0", "resolve-from": "^5.0.0", "semver": "7.5.4", "terminal-link": "^2.1.1"}}