#!/usr/bin/env node
import chalk from 'chalk';

import { logPlatformRunCommand } from './hints';
import { parseRunOptions } from './parseRunOptions';
import { Command } from '../../bin/cli';
import { PlatformDiscovery, platformRegistry } from '../core/PlatformRegistry';
import { assertWithOptionsArgs, printHelp } from '../utils/args';
import { CommandError, logCmdError } from '../utils/errors';
import { withExternalPlatformConfigAsync } from '../utils/externalPlatformConfig';

export const expoRun: Command = async (argv) => {
  const args = assertWithOptionsArgs(
    {
      // Types
      '--help': Boolean,
      // Aliases
      '-h': '--help',
    },
    {
      argv,
      // Allow additional flags for both android and ios commands
      permissive: true,
    }
  );

  try {
    // Get project root for config plugin integration (run command uses current directory)
    const { findUpProjectRootOrAssert } = await import('../utils/findUp.js');
    const projectRoot = findUpProjectRootOrAssert(process.cwd());

    // Load external platforms
    await PlatformDiscovery.loadExternalPlatforms(projectRoot);

    // Apply external platform config plugins to ensure consistent behavior
    const { getConfig } = await import('@expo/config');
    const baseConfig = getConfig(projectRoot);
    await withExternalPlatformConfigAsync(baseConfig.exp, projectRoot);

    let [platform] = args._ ?? [];

    // Workaround, filter `--flag` as platform
    if (platform?.startsWith('-')) {
      platform = '';
    }

    // Remove the platform from raw arguments, when provided
    const argsWithoutPlatform = !platform ? argv : argv?.splice(1);

    // Do not capture `--help` when platform is provided
    if (!platform && args['--help']) {
      // Build platform list including external platforms for help text
      const availablePlatforms = ['android', 'ios', ...platformRegistry.getAvailablePlatforms()];
      const platformList = availablePlatforms.join('|');

      printHelp(
        'Run the native app locally',
        `npx expo run <${platformList}>`,
        chalk`{dim $} npx expo run <${platformList}> --help  Output usage information`
      );
    }

    if (!platform) {
      const { selectAsync } = await import('../utils/prompts.js');

      // Build platform choices including external platforms
      const availablePlatforms = ['ios', 'android', ...platformRegistry.getAvailablePlatforms()];
      const platformChoices = availablePlatforms.map((p) => ({
        title: p.charAt(0).toUpperCase() + p.slice(1),
        value: p,
      }));

      platform = await selectAsync('Select the platform to run', platformChoices);
    }

    // Handle external platforms
    const externalPlatforms = platformRegistry.getAvailablePlatforms();

    if (externalPlatforms.includes(platform)) {
      // External platform detected - check for native integration
      const platformData = platformRegistry.getPlatform(platform);

      if (platformData?.runAsync) {
        // Platform provides native run integration - use delegation for 90-95% parity
        const options = parseRunOptions(argsWithoutPlatform || []);
        const { findUpProjectRootOrAssert } = await import('../utils/findUp.js');
        const projectRoot = findUpProjectRootOrAssert(process.cwd());
        return platformData.runAsync(projectRoot, options);
      } else {
        // Fallback to React Native CLI for platforms without native integration
        const { runExternalPlatformAsync } = await import('./external/runExternalPlatformAsync.js');
        return runExternalPlatformAsync(platform, argsWithoutPlatform);
      }
    }

    logPlatformRunCommand(platform, argsWithoutPlatform);

    // Handle built-in platforms (ios, android)
    if (platform === 'ios') {
      const { expoRunIos } = await import('./ios/index.js');
      return expoRunIos(argsWithoutPlatform);
    } else if (platform === 'android') {
      const { expoRunAndroid } = await import('./android/index.js');
      return expoRunAndroid(argsWithoutPlatform);
    } else {
      const availablePlatforms = ['ios', 'android', ...externalPlatforms];
      throw new CommandError(
        'UNSUPPORTED_PLATFORM',
        `Platform "${platform}" is not supported.\n\n` +
          `Available platforms: ${availablePlatforms.join(', ')}\n\n` +
          `If "${platform}" is an external platform, install it first:\n` +
          `  ${chalk.cyan(`npm install expo-platform-${platform}`)}\n\n` +
          `Then run the command again.`
      );
    }
  } catch (error: any) {
    logCmdError(error);
  }
};
