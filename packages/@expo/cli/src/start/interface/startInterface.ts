import chalk from 'chalk';

import { Key<PERSON>ressHandler } from './KeyPressHandler';
import { BLT, printHelp, printUsage, StartOptions } from './commandsTable';
import { DevServerManagerActions } from './interactiveActions';
import { openExternalPlatformAsync } from './openExternalPlatform';
import {
  assignPlatformKeys,
  findPlatformByKey,
  logKeyAssignments,
  PlatformKeyMapping,
} from './platformKeyAssignment';
import { platformRegistry } from '../../core/PlatformRegistry';
import * as Log from '../../log';
import { openInEditorAsync } from '../../utils/editor';
import { AbortCommandError } from '../../utils/errors';
import { getAllSpinners, ora } from '../../utils/ora';
import { getProgressBar, setProgressBar } from '../../utils/progress';
import { addInteractionListener, pauseInteractions } from '../../utils/prompts';
import { WebSupportProjectPrerequisite } from '../doctor/web/WebSupportProjectPrerequisite';
import { DevServerManager } from '../server/DevServerManager';

const debug = require('debug')('expo:start:interface:startInterface') as typeof console.log;

const CTRL_C = '\u0003';
const CTRL_D = '\u0004';
const CTRL_L = '\u000C';

const BUILT_IN_PLATFORM_SETTINGS: Record<
  string,
  { name: string; key: string; launchTarget: 'emulator' | 'simulator' | 'external' }
> = {
  android: {
    name: 'Android',
    key: 'android',
    launchTarget: 'emulator',
  },
  ios: {
    name: 'iOS',
    key: 'ios',
    launchTarget: 'simulator',
  },
};

/**
 * Get platform settings including external platforms.
 * External platforms use the run command for launching.
 */
function getAllPlatformSettings(): Record<
  string,
  { name: string; key: string; launchTarget: 'emulator' | 'simulator' | 'external' }
> {
  const settings = { ...BUILT_IN_PLATFORM_SETTINGS };

  // Add external platforms
  const externalPlatforms = platformRegistry.getAvailablePlatforms();
  for (const platform of externalPlatforms) {
    const platformData = platformRegistry.getPlatform(platform);
    if (platformData) {
      settings[platform] = {
        name: platformData.displayName || platform.charAt(0).toUpperCase() + platform.slice(1),
        key: platform,
        launchTarget: 'external', // External platforms use run command
      };
    }
  }

  return settings;
}

export async function startInterfaceAsync(
  devServerManager: DevServerManager,
  options: Pick<StartOptions, 'devClient' | 'platforms'>
) {
  const actions = new DevServerManagerActions(devServerManager, options);

  // Generate smart platform key assignments to avoid conflicts
  const { platforms = ['ios', 'android', 'web'] } = options;
  const platformKeyMappings = assignPlatformKeys(platforms);
  logKeyAssignments(platformKeyMappings);

  const isWebSocketsEnabled = devServerManager.getDefaultDevServer()?.isTargetingNative();

  const usageOptions = {
    isWebSocketsEnabled,
    devClient: devServerManager.options.devClient,
    ...options,
  };

  actions.printDevServerInfo(usageOptions);

  const onPressAsync = async (key: string) => {
    // Auxillary commands all escape.
    switch (key) {
      case CTRL_C:
      case CTRL_D: {
        // Prevent terminal UI from accepting commands while the process is closing.
        // Without this, fast typers will close the server then start typing their
        // next command and have a bunch of unrelated things pop up.
        pauseInteractions();

        const spinners = getAllSpinners();
        spinners.forEach((spinner) => {
          spinner.fail();
        });

        const currentProgress = getProgressBar();
        if (currentProgress) {
          currentProgress.terminate();
          setProgressBar(null);
        }
        const spinner = ora({ text: 'Stopping server', color: 'white' }).start();
        try {
          await devServerManager.stopAsync();
          spinner.stopAndPersist({ text: 'Stopped server', symbol: `\u203A` });
          // @ts-ignore: Argument of type '"SIGINT"' is not assignable to parameter of type '"disconnect"'.
          process.emit('SIGINT');

          // TODO: Is this the right place to do this?
          process.exit();
        } catch (error) {
          spinner.fail('Failed to stop server');
          throw error;
        }
        break;
      }
      case CTRL_L:
        return Log.clear();
      case '?':
        return printUsage(usageOptions, { verbose: true });
    }

    // Optionally enabled

    if (isWebSocketsEnabled) {
      switch (key) {
        case 'm':
          return actions.toggleDevMenu();
        case 'M':
          return actions.openMoreToolsAsync();
      }
    }

    const { platforms = ['ios', 'android', 'web'] } = options;
    const allPlatformSettings = getAllPlatformSettings();

    // Handle platform-specific keys using smart key assignment
    const platformKey = findPlatformByKey(key, platformKeyMappings);

    if (platformKey) {
      const shouldPrompt = key === key.toUpperCase(); // Uppercase keys trigger prompts
      if (shouldPrompt) {
        Log.clear();
      }

      const settings = allPlatformSettings[platformKey];
      if (process.env.EXPO_DEBUG) {
        console.debug(`Platform key found: ${platformKey}, settings:`, settings);
        console.debug('All platform settings:', Object.keys(allPlatformSettings));
      }

      if (!settings) {
        Log.warn(`Platform "${platformKey}" not found in platform settings`);
        return;
      }

      Log.log(`${BLT} Opening on ${settings.name}...`);

      if (!platforms.includes(settings.key as any)) {
        Log.warn(
          chalk`${settings.name} is disabled, enable it by adding {bold ${settings.key}} to the platforms array in your app.json or app.config.js`
        );
      } else {
        try {
          if (settings.launchTarget === 'external') {
            // For external platforms, use device manager integration if available
            const platformData = platformRegistry.getPlatform(settings.key);

            if (
              platformData?.deviceManagerConstructor &&
              platformData?.platformManagerConstructor
            ) {
              // Use proper device manager integration
              await openExternalPlatformAsync(devServerManager, settings.key, { shouldPrompt });
            } else {
              // Fallback to run command for platforms without device manager integration
              const { runExternalPlatformAsync } = await import(
                '../../run/external/runExternalPlatformAsync.js'
              );
              await runExternalPlatformAsync(settings.key, []);
            }
          } else {
            // For built-in platforms, use the dev server
            const server = devServerManager.getDefaultDevServer();
            await server.openPlatformAsync(settings.launchTarget as 'emulator' | 'simulator', {
              shouldPrompt,
            });
          }
          printHelp();
        } catch (error: any) {
          if (!(error instanceof AbortCommandError)) {
            Log.exception(error);
          }
        }
      }
      // Break out early.
      return;
    }

    switch (key) {
      case 's': {
        Log.clear();
        if (await devServerManager.toggleRuntimeMode()) {
          usageOptions.devClient = devServerManager.options.devClient;
          return actions.printDevServerInfo(usageOptions);
        }
        break;
      }
      case 'w': {
        try {
          await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);
          if (!platforms.includes('web')) {
            platforms.push('web');
            options.platforms?.push('web');
          }
        } catch (e: any) {
          Log.warn(e.message);
          break;
        }

        const isDisabled = !platforms.includes('web');
        if (isDisabled) {
          debug('Web is disabled');
          // Use warnings from the web support setup.
          break;
        }

        // Ensure the Webpack dev server is running first
        if (!devServerManager.getWebDevServer()) {
          debug('Starting up webpack dev server');
          await devServerManager.ensureWebDevServerRunningAsync();
          // When this is the first time webpack is started, reprint the connection info.
          actions.printDevServerInfo(usageOptions);
        }

        Log.log(`${BLT} Open in the web browser...`);
        try {
          await devServerManager.getWebDevServer()?.openPlatformAsync('desktop');
          printHelp();
        } catch (error: any) {
          if (!(error instanceof AbortCommandError)) {
            Log.exception(error);
          }
        }
        break;
      }
      case 'c':
        Log.clear();
        return actions.printDevServerInfo(usageOptions);
      case 'j':
        return actions.openJsInspectorAsync();
      case 'r':
        return actions.reloadApp();
      case 'o':
        Log.log(`${BLT} Opening the editor...`);
        return openInEditorAsync(devServerManager.projectRoot);
    }
  };

  const keyPressHandler = new KeyPressHandler(onPressAsync);

  const listener = keyPressHandler.createInteractionListener();

  addInteractionListener(listener);

  // Start observing...
  keyPressHandler.startInterceptingKeyStrokes();
}
