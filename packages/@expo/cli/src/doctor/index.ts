#!/usr/bin/env node
import { getConfig } from '@expo/config';
import chalk from 'chalk';

import { Command } from '../../bin/cli';
import { PlatformDiscovery, platformRegistry } from '../core/PlatformRegistry';
import * as Log from '../log';
import { assertArgs, printHelp } from '../utils/args';
import { CommandError, logCmdError } from '../utils/errors';
import { findUpProjectRootOrAssert } from '../utils/findUp';

/**
 * Doctor command for checking external platform prerequisites and health.
 * This integrates with external platforms to validate their development environments.
 */
export async function doctorAsync(projectRoot: string): Promise<void> {
  try {
    Log.log(chalk.gray(`Checking project health at ${projectRoot}`));

    // Load external platforms
    await PlatformDiscovery.loadExternalPlatforms(projectRoot);

    // Get project configuration to determine which platforms are enabled
    const { exp } = getConfig(projectRoot, { skipPlugins: true });
    const platforms = exp.platforms || ['ios', 'android'];
    const externalPlatforms = platforms.filter(p => !['ios', 'android', 'web'].includes(p));

    if (externalPlatforms.length === 0) {
      Log.log(chalk.green('✓ No external platforms detected - nothing to check'));
      return;
    }

    Log.log(chalk.cyan(`Found external platforms: ${externalPlatforms.join(', ')}`));
    Log.log('');

    let hasErrors = false;
    const results: Array<{ platform: string; success: boolean; error?: string }> = [];

    // Check each external platform
    for (const platformName of externalPlatforms) {
      const platformData = platformRegistry.getPlatform(platformName);

      if (!platformData) {
        const error = `Platform "${platformName}" not found in registry`;
        Log.log(chalk.red(`✗ ${platformName}: ${error}`));
        results.push({ platform: platformName, success: false, error });
        hasErrors = true;
        continue;
      }

      if (!platformData.prerequisiteConstructor) {
        Log.log(chalk.yellow(`⚠ ${platformName}: No health checks available`));
        results.push({ platform: platformName, success: true });
        continue;
      }

      try {
        Log.log(chalk.gray(`Checking ${platformName}...`));
        const prerequisite = new platformData.prerequisiteConstructor(platformName);
        await prerequisite.assertAsync();
        Log.log(chalk.green(`✓ ${platformName}: All checks passed`));
        results.push({ platform: platformName, success: true });
      } catch (error: any) {
        Log.log(chalk.red(`✗ ${platformName}: ${error.message}`));
        results.push({ platform: platformName, success: false, error: error.message });
        hasErrors = true;
      }
    }

    // Summary
    Log.log('');
    Log.log(chalk.bold('Summary:'));
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    if (hasErrors) {
      Log.log(chalk.red(`${successCount}/${totalCount} platforms passed health checks`));
      Log.log('');
      Log.log(chalk.yellow('To fix issues:'));
      Log.log('1. Follow the installation instructions shown above');
      Log.log('2. Run this command again to verify fixes');
      Log.log('3. Check platform-specific documentation for troubleshooting');
      process.exit(1);
    } else {
      Log.log(chalk.green(`${successCount}/${totalCount} platforms passed health checks`));
      Log.log(chalk.green('All external platforms are ready for development!'));
    }
  } catch (error: any) {
    logCmdError(error);
  }
}

/**
 * Doctor command entry point.
 */
export const expoDoctorCommand: Command = async (argv) => {
  const args = assertArgs(
    {
      // Types
      '--help': Boolean,
      // Aliases
      '-h': '--help',
    },
    argv
  );

  if (args['--help']) {
    printHelp(
      'Check external platform prerequisites and health',
      'npx expo doctor',
      [
        'This command checks the health of external platforms in your project.',
        'It validates development environments, system requirements, and platform tools.',
        '',
        'External platforms can provide their own health checks by implementing',
        'the ExternalPlatformPrerequisite interface.',
      ].join('\n'),
      ['-h, --help    Usage info'].join('\n')
    );
  }

  const projectRoot = findUpProjectRootOrAssert(process.cwd());
  await doctorAsync(projectRoot);
};
