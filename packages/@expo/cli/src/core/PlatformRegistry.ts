// Import existing base classes from Expo CLI
import { AppIdResolver } from '../start/platforms/AppIdResolver';
import { DeviceManager } from '../start/platforms/DeviceManager';
import { PlatformManager, BaseResolveDeviceProps } from '../start/platforms/PlatformManager';

/**
 * External platform device manager constructor.
 * External platforms should provide a DeviceManager class that extends the base DeviceManager.
 * The class must implement a static resolveAsync method for device resolution.
 */
export interface ExternalPlatformDeviceManagerConstructor<TDevice> {
  new (device: TDevice): DeviceManager<TDevice>;
  resolveAsync(options?: BaseResolveDeviceProps<TDevice>): Promise<DeviceManager<TDevice>>;
}

/**
 * External platform manager constructor.
 * Creates a PlatformManager instance that extends Expo's base class.
 */
export type ExternalPlatformManagerConstructor<TDevice> = new (
  projectRoot: string,
  options: {
    getDevServerUrl: () => string | null;
    getExpoGoUrl: () => string;
    getRedirectUrl: () => string | null;
    getCustomRuntimeUrl: (props?: { scheme?: string }) => string | null;
    resolveDeviceAsync: (
      options?: BaseResolveDeviceProps<TDevice>
    ) => Promise<DeviceManager<TDevice>>;
  }
) => PlatformManager<TDevice>;

/**
 * External platform app ID resolver constructor.
 */
export type ExternalPlatformAppIdResolverConstructor = new (projectRoot: string) => AppIdResolver;

/**
 * External platform dependency resolver interface.
 * Allows external platforms to provide platform-specific dependency resolution.
 */
export interface ExternalPlatformDependencyResolver {
  /**
   * Resolve platform-specific dependencies for given packages.
   *
   * @param packages List of packages being installed
   * @param sdkVersion Current Expo SDK version
   * @returns Promise resolving to additional packages that should be installed
   */
  resolveDependencies(packages: string[], sdkVersion: string): Promise<string[]>;

  /**
   * Get platform-specific autolinking configuration.
   * This is called after package installation to configure autolinking.
   *
   * @param projectRoot Project root directory
   * @param packages List of installed packages
   * @returns Promise resolving when autolinking is configured
   */
  configureAutolinking?(projectRoot: string, packages: string[]): Promise<void>;
}

/**
 * Autolinking implementation interface for platform-specific native module integration
 */
export interface AutolinkingImplementation {
  resolveModuleAsync(moduleName: string, projectRoot: string): Promise<any>;
  generatePackageListAsync(projectRoot: string): Promise<string>;
  getLinkingConfigAsync(projectRoot: string): Promise<any>;
}

/**
 * SDK module compatibility declaration
 */
export interface SDKModuleCompatibility {
  [moduleName: string]: {
    supported: boolean;
    version?: string;
    implementation?: string; // Path to platform-specific implementation
    fallback?: string; // Fallback behavior when unsupported
  };
}

/**
 * Asset handler configuration for platform-specific asset processing
 */
export interface AssetHandlerConfiguration {
  appIcon?: {
    sizes: number[];
    format: string;
    generator?: string;
  };
  splashScreen?: {
    supported: boolean;
    fallback?: string;
    handler?: string;
  };
  fonts?: {
    formats: string[];
    handler?: string;
  };
}

/**
 * Dev client extension configuration for platform-specific development tools
 */
export interface DevClientExtensionConfiguration {
  devMenuItems?: {
    name: string;
    action: string;
    icon?: string;
  }[];
  errorBoundaries?: {
    [errorType: string]: string; // Path to error boundary component
  };
  debugTools?: {
    [toolName: string]: string; // Path to debug tool implementation
  };
  inspectors?: {
    [inspectorName: string]: string; // Path to inspector implementation
  };
}

/**
 * Test utility configuration for platform-specific testing support
 */
export interface TestUtilityConfiguration {
  testEnvironment?: string; // Path to Jest test environment
  setupFiles?: string[]; // Paths to test setup files
  moduleNameMapper?: {
    [pattern: string]: string;
  };
  platformMocks?: {
    [mockName: string]: any;
  };
  testUtilities?: {
    [utilityName: string]: string; // Path to test utility implementation
  };
}

/**
 * Permission configuration for platform-specific permission handling
 */
export interface PlatformPermissionConfiguration {
  [permissionName: string]: {
    key: string; // Platform-specific permission key
    description: string; // Human-readable description
    required: boolean; // Whether this permission is required
    fallback?: string; // Fallback behavior when permission denied
  };
}

/**
 * Generic run options interface for external platforms.
 * Provides a common interface that external platforms can use for their run commands.
 */
export interface RunOptions {
  /** Target device (device ID, 'device', 'emulator', or boolean for prompting) */
  device?: string | boolean;
  /** Dev server port to use */
  port?: number;
  /** Should start the bundler dev server */
  bundler?: boolean;
  /** Should install missing dependencies before building */
  install?: boolean;
  /** Should use build cache */
  buildCache?: boolean;
  /** Path to an existing binary to install on the device */
  binary?: string;
  /** Build variant/configuration (e.g., 'debug', 'release') */
  variant?: string;
  /** Build configuration (e.g., 'Debug', 'Release') */
  configuration?: string;
  /** Build scheme (platform-specific) */
  scheme?: string | boolean;
  /** Additional platform-specific options */
  [key: string]: any;
}

/**
 * External platform interface with essential integration points.
 * Enables full out-of-tree platform integration while remaining lean.
 */
export interface ExternalPlatform {
  /** Platform name (e.g., 'macos', 'windows') */
  platform: string;
  /** Display name for the platform (e.g., 'macOS', 'Windows') */
  displayName?: string;

  // Core Integration Points (Phase 0)
  /** Device manager constructor that extends DeviceManager base class */
  deviceManagerConstructor?: ExternalPlatformDeviceManagerConstructor<any>;
  /** Platform manager constructor that extends Expo's PlatformManager base class */
  platformManagerConstructor?: ExternalPlatformManagerConstructor<any>;
  /** App ID resolver constructor for this platform */
  appIdResolverConstructor?: ExternalPlatformAppIdResolverConstructor;

  // Run Command Integration (Phase 1)
  /** Optional run function for native integration - enables 90-95% run command parity */
  runAsync?: (projectRoot: string, options: RunOptions) => Promise<void>;

  // Install Command Integration (Phase 1)
  /** Optional dependency resolver for platform-specific package management */
  dependencyResolver?: ExternalPlatformDependencyResolver;

  // Build System Integration (Phase 1)
  /** Config plugins to auto-add during prebuild */
  configPlugins?: string[];
  /** Metro file extensions for platform-specific files */
  metroExtensions?: string[];
  /** Template path for expo prebuild */
  templatePath?: string;
  /** Autolinking implementation for native module integration */
  autolinkingImplementation?: AutolinkingImplementation;

  // SDK Integration (Phase 1)
  /** SDK module compatibility declarations */
  supportedModules?: SDKModuleCompatibility;

  // Asset Management (Phase 1)
  /** Asset handler configuration for platform-specific assets */
  assetHandlers?: AssetHandlerConfiguration;

  // Development Tools Integration (Phase 2)
  /** Dev client extension configuration for development tools */
  devClientExtensions?: DevClientExtensionConfiguration;
  /** Test utility configuration for platform-specific testing */
  testUtilities?: TestUtilityConfiguration;
  /** Permission configuration for platform-specific permissions */
  permissions?: PlatformPermissionConfiguration;

  // Advanced Platform Features (Phase 3)
  /** Advanced debugging configuration for platform-specific debugging tools */
  debuggingConfig?: any; // Will be typed as PlatformDebuggingConfiguration when imported
  /** Advanced Metro configuration for platform-specific bundling */
  metroConfig?: any; // Will be typed as PlatformMetroConfiguration when imported
  /** Platform-specific validation rules */
  validationRules?: any[]; // Will be typed as PlatformValidationRule[] when imported
}

/**
 * Centralized error messages for external platform integration.
 * This helps maintain consistency across all platform-related error messages.
 */
class PlatformErrorMessages {
  static platformPackageLoadFailed(packageName: string, error: string): string {
    const platformName = packageName.replace('expo-platform-', '');
    return (
      `⚠️  Failed to load external platform package "${packageName}".\n` +
      `   This package will be ignored. Error: ${error}\n\n` +
      `   Common solutions:\n` +
      `   • Reinstall the package: npm install ${packageName}\n` +
      `   • Check package compatibility with your Expo SDK version\n` +
      `   • Verify the package exports valid platform data\n` +
      `   • Check the package documentation for setup requirements\n\n` +
      `   The ${platformName} platform will not be available until this is resolved.`
    );
  }

  static debugPlatformLoaded(packageName: string): string {
    return `Successfully loaded platform package: ${packageName}`;
  }

  static debugNodeModulesNotFound(): string {
    return 'node_modules directory not found, skipping platform discovery';
  }

  static debugNodeModulesReadFailed(error: string): string {
    return `Failed to read node_modules directory: ${error}`;
  }
}

/**
 * Simple registry for tracking external platforms.
 * Focuses only on essential platform management for Phase 0.
 */
export class PlatformRegistry {
  private platforms = new Map<string, ExternalPlatform>();

  /**
   * Register an external platform.
   * @param platformData The platform data from expo-platform-* package
   */
  register(platformData: ExternalPlatform): void {
    this.platforms.set(platformData.platform, platformData);

    // Auto-register development tools if available
    this.registerDevelopmentTools(platformData);

    // Auto-register Phase 3 advanced features if available
    this.registerAdvancedFeatures(platformData);
  }

  /**
   * Register development tools for a platform.
   * This is called automatically when a platform is registered.
   * @param platformData The platform data
   */
  private registerDevelopmentTools(platformData: ExternalPlatform): void {
    // Import here to avoid circular dependencies
    const { devClientExtensionRegistry } = require('./DevClientExtensionRegistry');

    if (platformData.devClientExtensions) {
      devClientExtensionRegistry.registerDevClientExtensions(
        platformData.platform,
        platformData.devClientExtensions
      );
    }

    if (platformData.testUtilities) {
      devClientExtensionRegistry.registerTestUtilities(
        platformData.platform,
        platformData.testUtilities
      );
    }

    if (platformData.permissions) {
      devClientExtensionRegistry.registerPermissions(
        platformData.platform,
        platformData.permissions
      );
    }
  }

  /**
   * Register advanced features for a platform.
   * This is called automatically when a platform is registered.
   * @param platformData The platform data
   */
  private registerAdvancedFeatures(platformData: ExternalPlatform): void {
    // Register validation rules if available
    if (platformData.validationRules && Array.isArray(platformData.validationRules)) {
      try {
        // Import here to avoid circular dependencies
        const { PlatformConfigValidation } = require('./PlatformConfigValidation');
        PlatformConfigValidation.registerValidationRules(platformData.validationRules);
      } catch (error: any) {
        // Silently fail if PlatformConfigValidation is not available
        // This allows platforms to work even if Phase 3 features are not loaded
      }
    }
  }

  /**
   * Get all available external platform names.
   * @returns Array of platform names
   */
  getAvailablePlatforms(): string[] {
    return Array.from(this.platforms.keys());
  }

  /**
   * Check if a platform is registered.
   * @param platform The platform identifier
   * @returns True if the platform is registered
   */
  hasPlatform(platform: string): boolean {
    return this.platforms.has(platform);
  }

  /**
   * Get platform data.
   * @param platform The platform identifier
   * @returns Platform data or undefined if not found
   */
  getPlatform(platform: string): ExternalPlatform | undefined {
    return this.platforms.get(platform);
  }

  /**
   * Get all registered platforms.
   * @returns Array of platform data
   */
  getAllPlatforms(): ExternalPlatform[] {
    return Array.from(this.platforms.values());
  }

  /**
   * Clear all registered platforms.
   * This is primarily useful for testing.
   */
  clear(): void {
    this.platforms.clear();
  }
}

/**
 * Global platform registry instance.
 * This is the main registry used throughout the CLI.
 */
export const platformRegistry = new PlatformRegistry();

/**
 * Simple platform discovery service that finds expo-platform-* packages.
 * This focuses on auto-discovery and auto-wiring without complex caching or validation.
 */
export class PlatformDiscovery {
  private static loadedPlatforms = new Set<string>();

  /**
   * Core implementation for loading external platform packages.
   * This handles both sync and async loading scenarios.
   */
  private static loadExternalPlatformsCore(
    projectRoot?: string,
    isAsync: boolean = true
  ): Promise<void> | void {
    const { readdirSync, existsSync } = require('fs');
    const { resolve } = require('path');
    const debug = require('debug')('expo:platform-discovery') as typeof console.log;

    const resolvedProjectRoot = projectRoot || process.cwd();
    const nodeModulesPath = resolve(resolvedProjectRoot, 'node_modules');

    if (!existsSync(nodeModulesPath)) {
      debug(PlatformErrorMessages.debugNodeModulesNotFound());
      return isAsync ? Promise.resolve() : undefined;
    }

    const loadPlatforms = () => {
      try {
        const entries = readdirSync(nodeModulesPath);
        const platformPackages = entries.filter((entry: string) =>
          entry.startsWith('expo-platform-')
        );

        for (const packageName of platformPackages) {
          if (!this.loadedPlatforms.has(packageName)) {
            try {
              this.loadPlatformPackageCore(nodeModulesPath, packageName);
              this.loadedPlatforms.add(packageName);
              debug(PlatformErrorMessages.debugPlatformLoaded(packageName));
            } catch (error: any) {
              console.warn(
                PlatformErrorMessages.platformPackageLoadFailed(packageName, error.message)
              );
            }
          }
        }
      } catch (error: any) {
        debug(PlatformErrorMessages.debugNodeModulesReadFailed(error.message));
      }
    };

    if (isAsync) {
      return Promise.resolve().then(loadPlatforms);
    } else {
      loadPlatforms();
    }
  }

  /**
   * Load external platform packages from node_modules.
   * Looks for packages matching 'expo-platform-*' pattern.
   */
  static async loadExternalPlatforms(projectRoot?: string): Promise<void> {
    return this.loadExternalPlatformsCore(projectRoot, true) as Promise<void>;
  }

  /**
   * Load a specific platform package and register it.
   * This is the core implementation that handles both sync and async loading.
   */
  private static loadPlatformPackageCore(nodeModulesPath: string, packageName: string): void {
    const { resolve } = require('path');
    const { existsSync, readFileSync } = require('fs');
    const resolveFrom = require('resolve-from');

    const packagePath = resolve(nodeModulesPath, packageName);
    const packageJsonPath = resolve(packagePath, 'package.json');

    if (!existsSync(packageJsonPath)) {
      throw new Error(`Platform package ${packageName} missing package.json`);
    }

    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'));

    if (!packageJson.main) {
      throw new Error(`Platform package ${packageName} missing main entry point`);
    }

    // Use resolveFrom to properly resolve the module
    const modulePath = resolveFrom(packagePath, '.');
    const platformModule = require(modulePath);

    // Check if it exports platform data
    if (platformModule.default && typeof platformModule.default === 'object') {
      // ES module with default export
      const platformData = platformModule.default as ExternalPlatform;
      platformRegistry.register(platformData);
    } else if (typeof platformModule === 'object' && platformModule.platform) {
      // CommonJS module with platform data
      const platformData = platformModule as ExternalPlatform;
      platformRegistry.register(platformData);
    } else {
      throw new Error(`Platform package ${packageName} does not export valid platform data`);
    }
  }

  /**
   * Load external platform packages synchronously.
   * This is used by Metro configuration which requires synchronous loading.
   * Looks for packages matching 'expo-platform-*' pattern.
   */
  static loadExternalPlatformsSync(projectRoot?: string): void {
    this.loadExternalPlatformsCore(projectRoot, false);
  }

  /**
   * Get the list of loaded platform package names.
   */
  static getLoadedPlatformPackages(): string[] {
    return Array.from(this.loadedPlatforms);
  }
}
