/**
 * Public API for external platform packages.
 * 
 * This module exports the types and classes that external platform packages
 * need to integrate with the Expo CLI. This provides a stable API surface
 * for external platforms to import from.
 */

// Export platform registry types and classes
export {
  ExternalPlatform,
  ExternalPlatformPrerequisite,
  ExternalPlatformPrerequisiteConstructor,
  ExternalPlatformDeviceManagerConstructor,
  ExternalPlatformManagerConstructor,
  ExternalPlatformAppIdResolverConstructor,
  ExternalPlatformDependencyResolver,
  PlatformRegistry,
  platformRegistry,
  PlatformDiscovery,
} from './core/PlatformRegistry';

// Export base classes that external platforms extend
export { DeviceManager } from './start/platforms/DeviceManager';
export { PlatformManager } from './start/platforms/PlatformManager';
export { AppIdResolver } from './start/platforms/AppIdResolver';

// Export utility types
export type { RunOptions } from './run/resolveOptions';
