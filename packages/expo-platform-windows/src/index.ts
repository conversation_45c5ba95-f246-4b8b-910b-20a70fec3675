/**
 * Expo Platform Windows - External Platform Implementation
 *
 * This package provides complete Windows platform support for Expo applications
 * through the External Platform System. It implements all integration points
 * required for 100% feature parity with built-in iOS/Android platforms.
 */

import { ExternalPlatform } from '@expo/cli/src/core/PlatformRegistry';

import { WindowsAutolinking } from './autolinking/WindowsAutolinking';
import { WindowsDeviceManager } from './deviceManager/WindowsDeviceManager';
import { WindowsPlatformManager } from './platformManager/WindowsPlatformManager';

/**
 * Main Windows platform definition that provides complete integration
 * with the Expo External Platform System.
 */
export const windowsPlatform: ExternalPlatform = {
  platform: 'windows',
  displayName: 'Windows',

  // Development workflow integration (now properly implemented with base class extensions)
  resolveDeviceAsync: WindowsDeviceManager.resolveAsync,
  platformManagerConstructor: WindowsPlatformManager,

  // Build system integration
  configPlugins: [
    'withWindowsManifest',
    'withWindowsAssets',
    'withWindowsPermissions',
    'withWindowsNewArch',
  ],

  // Metro bundler integration
  metroExtensions: [
    '.windows.js',
    '.windows.ts',
    '.windows.tsx',
    '.windows.jsx',
    '.win32.js',
    '.win32.ts',
    '.win32.tsx',
    '.win32.jsx',
  ],

  // Native module autolinking
  autolinkingImplementation: new WindowsAutolinking(),

  // Prebuild template path (points to template root, not platform subdirectory)
  templatePath: './templates',

  // SDK module compatibility declarations
  supportedModules: {
    // Core modules with full Windows support
    'expo-constants': { supported: true },
    'expo-file-system': { supported: true },
    'expo-linking': { supported: true },
    'expo-web-browser': { supported: true },
    'expo-clipboard': { supported: true },
    'expo-crypto': { supported: true },
    'expo-device': { supported: true },
    'expo-font': { supported: true },
    'expo-keep-awake': { supported: true },
    'expo-linear-gradient': { supported: true },
    'expo-status-bar': { supported: true },
    'expo-mesh-gradient': { supported: true }, // New in SDK 52

    // Modules with Windows-specific implementations
    'expo-camera': {
      supported: true,
      implementation: 'expo-camera/windows',
    },
    'expo-image': {
      supported: true,
      implementation: 'expo-image/windows',
    },
    'expo-av': {
      supported: true,
      implementation: 'expo-av/windows',
    },
    'expo-audio': {
      supported: true,
      implementation: 'expo-audio/windows',
    },
    'expo-video': {
      supported: true,
      implementation: 'expo-video/windows',
    },

    // Modules with graceful degradation
    'expo-notifications': {
      supported: false,
      fallback: 'graceful-degradation',
    },
    'expo-location': {
      supported: false,
      fallback: 'web-implementation',
    },
    'expo-sensors': {
      supported: false,
      fallback: 'graceful-degradation',
    },
  },

  // Asset management configuration
  assetHandlers: {
    appIcon: {
      sizes: [16, 24, 32, 48, 64, 128, 256],
      format: 'ico',
      generator: 'expo-platform-windows/assets/generateWindowsIcons',
    },
    splashScreen: {
      supported: true,
      handler: 'expo-platform-windows/assets/handleWindowsSplash',
    },
    fonts: {
      formats: ['ttf', 'otf', 'woff', 'woff2'],
      handler: 'expo-platform-windows/assets/handleWindowsFonts',
    },
  },

  // Windows permission system
  permissions: {
    camera: {
      key: 'webcam',
      description: 'Access camera for photo and video capture',
      required: false,
    },
    microphone: {
      key: 'microphone',
      description: 'Access microphone for audio recording',
      required: false,
    },
    location: {
      key: 'location',
      description: 'Access device location',
      required: false,
    },
    internetClient: {
      key: 'internetClient',
      description: 'Access internet for network requests',
      required: true,
    },
    picturesLibrary: {
      key: 'picturesLibrary',
      description: 'Access pictures library',
      required: false,
    },
    videosLibrary: {
      key: 'videosLibrary',
      description: 'Access videos library',
      required: false,
    },
    musicLibrary: {
      key: 'musicLibrary',
      description: 'Access music library',
      required: false,
    },
  },
};

// Export the platform as default for easy importing
export default windowsPlatform;

// Re-export all implementation classes for advanced usage
export { WindowsDeviceManager } from './deviceManager/WindowsDeviceManager';
export { WindowsPlatformManager } from './platformManager/WindowsPlatformManager';
export { WindowsAutolinking } from './autolinking/WindowsAutolinking';

// Re-export config plugins
export * from './configPlugins';

// Re-export asset handlers
export * from './assets';

// Re-export development tools
export * from './devClient';
